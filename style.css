/* ===== 基础工具样式 ===== */
.xiaobaix-iframe {
    transition: height 0.3s ease;
}

pre:has(+ .xiaobaix-iframe) {
    display: none;
}

.dark-number-input {
    background-color: var(--SmartThemeShadowColor) !important;
    color: var(--SmartThemeText) !important;
    border-color: var(--SmartThemeBorderColor) !important;
}

/* ===== 记忆按钮样式 ===== */
.mes_btn.memory-button {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.mes_btn.memory-button:hover {
    opacity: 1;
}

.mes_btn.memory-button.has-memory {
    color: var(--SmartThemeAccent);
}

/* ===== 模态框基础样式 ===== */
.memory-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--black30a);
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
    -webkit-backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease;
}

/* 宽屏模式 - 左侧面板 */
@media screen and (min-width: 1001px) {
    .memory-modal.behavior-modal,
    .memory-modal.main-menu-modal {
        justify-content: flex-start;
        align-items: stretch;
        background: transparent;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
        pointer-events: none;
    }

    .memory-modal.behavior-modal .memory-modal-content,
    .memory-modal.main-menu-modal .memory-modal-content {
        width: calc((100vw - var(--sheldWidth) - 2px) / 2);
        max-height: calc(100vh - var(--topBarBlockSize));
        height: calc(100vh - var(--topBarBlockSize));
        position: fixed;
        top: var(--topBarBlockSize);
        left: 0;
        margin: 0;
        border-radius: 0 10px 10px 0;
        border-left: none;
        animation: slideInLeft 0.3s ease;
        pointer-events: all;
    }
}

/* 窄屏模式 - 全屏 */
@media screen and (max-width: 1000px) {
    .memory-modal.behavior-modal .memory-modal-content,
    .memory-modal.main-menu-modal .memory-modal-content {
        width: 100vw !important;
        height: calc(100vh - var(--topBarBlockSize));
        max-width: 100vw !important;
        max-height: calc(100vh - var(--topBarBlockSize));
        position: fixed;
        top: var(--topBarBlockSize);
        left: 0;
        margin: 0;
        border-radius: 0 0 20px 20px;
        border-top: none;
        animation: slideInTop 0.3s ease;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInTop {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.memory-modal-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 10px;
    width: 85%;
    max-width: 700px;
    max-height: 85vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 0px 14px var(--black70a);
    animation: slideIn 0.3s ease;
    color: var(--SmartThemeBodyColor);
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.memory-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--SmartThemeBorderColor);
    background: var(--SmartThemeBlurTintColor);
    border-radius: 10px 10px 0 0;
}

@media screen and (min-width: 1001px) {
    .memory-modal.behavior-modal .memory-modal-header,
    .memory-modal.main-menu-modal .memory-modal-header {
        border-radius: 0 10px 0 0;
    }
}

@media screen and (max-width: 1000px) {
    .memory-modal.behavior-modal .memory-modal-header,
    .memory-modal.main-menu-modal .memory-modal-header {
        border-radius: 0;
        position: relative;
    }

    .memory-modal.behavior-modal .memory-modal-close,
    .memory-modal.main-menu-modal .memory-modal-close {
        position: absolute;
        top: 8px;
        right: 8px;
        font-size: 1.4em;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: var(--black30a);
    }
}

.memory-modal-title {
    font-size: var(--mainFontSize);
    font-weight: 600;
    color: var(--SmartThemeBodyColor);
}

.memory-modal-close {
    font-size: 1.2em;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
    color: var(--SmartThemeBodyColor);
    padding: 4px 8px;
    border-radius: 4px;
}

.memory-modal-close:hover {
    opacity: 1;
    background: var(--white30a);
}

.memory-tab-content {
    padding: 8px 12px;
    overflow-y: auto;
    flex-grow: 1;
    font-size: calc(var(--mainFontSize) * 0.95);
    line-height: 1.4;
}

#memory-stats-content {
    padding: 8px 12px;
    overflow-y: auto;
    flex-grow: 1;
    font-size: calc(var(--mainFontSize) * 0.95);
    line-height: 1.4;
    white-space: pre-wrap; /* 保持换行和空格 */
    word-wrap: break-word; /* 长单词换行 */
}

@media screen and (min-width: 1001px) {
    .memory-modal.behavior-modal .memory-tab-content,
    .memory-modal.main-menu-modal .memory-tab-content {
        max-height: calc(100vh - var(--topBarBlockSize) - 120px);
    }
}

@media screen and (max-width: 1000px) {
    .memory-modal.behavior-modal .memory-tab-content,
    .memory-modal.main-menu-modal .memory-tab-content {
        max-height: calc(100vh - var(--topBarBlockSize) - 140px);
        padding: 6px 10px;
    }
}

.memory-modal-footer {
    padding: 12px 16px;
    border-top: 1px solid var(--SmartThemeBorderColor);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    background: var(--SmartThemeBlurTintColor);
    border-radius: 0 0 10px 10px;
    flex-wrap: wrap;
}

@media screen and (min-width: 1001px) {
    .memory-modal.behavior-modal .memory-modal-footer,
    .memory-modal.main-menu-modal .memory-modal-footer {
        border-radius: 0 0 10px 0;
    }
}

@media screen and (max-width: 1000px) {
    .memory-modal.behavior-modal .memory-modal-footer,
    .memory-modal.main-menu-modal .memory-modal-footer {
        border-radius: 0;
        flex-direction: column;
        gap: 8px;
    }

    .behavior-footer-left,
    .behavior-footer-right,
    .main-menu-footer-buttons {
        width: 100%;
        justify-content: center;
    }

    .main-menu-footer-buttons {
        flex-direction: column;
    }

    .main-menu-footer-buttons .memory-action-button {
        width: 100%;
        max-width: 200px;
    }
}

.behavior-footer-left,
.behavior-footer-right,
.main-menu-footer-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.memory-action-button {
    color: var(--SmartThemeBodyColor);
    background-color: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: calc(var(--mainFontSize) * 0.9);
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
    min-height: 28px;
    filter: grayscale(0.3);
}

.memory-action-button:hover {
    background-color: var(--white30a);
    filter: grayscale(0);
    transform: translateY(-1px);
}

.memory-action-button.secondary {
    filter: grayscale(0.6);
}

.memory-action-button.primary {
    background-color: var(--crimson70a);
    color: var(--white);
    filter: grayscale(0);
}

.memory-action-button.primary:hover {
    background-color: var(--crimson);
}

/* ===== StatsTracker 专用样式 ===== */
.behavior-modal-content {
    width: 90%;
    max-width: 800px;
    max-height: 85vh;
}

/* 重新设计的卡片式布局 */
.behavior-card {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.behavior-card:hover {
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 2px 8px var(--black30a);
}

.behavior-card-header {
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--SmartThemeBlurTintColor) 0%, var(--black10a) 100%);
    border-bottom: 1px solid var(--SmartThemeBorderColor);
}

.behavior-card-title {
    font-size: calc(var(--mainFontSize) * 1.05);
    font-weight: 600;
    color: var(--SmartThemeBodyColor);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.behavior-card-desc {
    font-size: calc(var(--mainFontSize) * 0.85);
    color: var(--SmartThemeBodyColor);
    opacity: 0.8;
    margin: 4px 0 0 0;
    line-height: 1.3;
}

.behavior-card-content {
    padding: 16px;
}

/* 介绍卡片 */
.behavior-intro-card {
    margin-bottom: 16px;
}

.behavior-intro-content {
    font-size: calc(var(--mainFontSize) * 0.9);
    line-height: 1.4;
    color: var(--SmartThemeBodyColor);
}

.current-character {
    margin-top: 8px;
    padding: 8px 12px;
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border-radius: 6px;
    font-weight: 500;
}

/* 追踪人物卡片 */
.tracked-names-card {
    margin-bottom: 16px;
}

.tracked-names-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
    min-height: 32px;
    padding: 12px;
    background: var(--black10a);
    border-radius: 6px;
    border: 1px dashed var(--SmartThemeBorderColor);
}

.tracked-name-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border-radius: 16px;
    font-size: calc(var(--mainFontSize) * 0.85);
    font-weight: 500;
    transition: all 0.2s ease;
}

.tracked-name-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--black30a);
}

.remove-name {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    font-size: 1.1em;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    opacity: 0.7;
}

.remove-name:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
}

.add-name-container {
    display: flex;
    gap: 8px;
}

.tracked-name-input {
    flex: 1;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: calc(var(--mainFontSize) * 0.9);
    transition: all 0.2s ease;
}

.tracked-name-input:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

.add-name-button {
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border: 1px solid var(--SmartThemeAccent);
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: calc(var(--mainFontSize) * 0.9);
    font-weight: 500;
    white-space: nowrap;
}

.add-name-button:hover {
    background: var(--SmartThemeAccentHover);
    transform: translateY(-1px);
}

/* 阶段选择器 */
.behavior-stages-card {
    margin-bottom: 16px;
}

.behavior-stages-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 6px;
    padding: 4px;
}

@media screen and (max-width: 1000px) {
    .behavior-stages-selector {
        grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
        gap: 4px;
    }
}

.behavior-stage-tab {
    padding: 8px 12px;
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: calc(var(--mainFontSize) * 0.85);
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--SmartThemeBodyColor);
}

@media screen and (max-width: 1000px) {
    .behavior-stage-tab {
        padding: 6px 8px;
        font-size: calc(var(--mainFontSize) * 0.8);
    }
}

.behavior-stage-tab:hover {
    background: var(--white20a);
    transform: translateY(-1px);
}

.behavior-stage-tab.active {
    background: var(--SmartThemeAccent);
    color: var(--SmartThemeAccentText);
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 2px 8px var(--SmartThemeAccent30a);
}

/* 阶段内容区 */
.behavior-stage-content {
    /* 保持原位置，只更新样式 */
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 8px;
    padding: 0;
    overflow: hidden;
}

.behavior-stage-form {
    padding: 20px;
}

.behavior-stage-form h3 {
    margin: 0 0 16px 0;
    color: var(--SmartThemeBodyColor);
    font-size: calc(var(--mainFontSize) * 1.1);
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--SmartThemeAccent);
}

.behavior-field {
    margin-bottom: 16px;
}

.behavior-field label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    font-size: calc(var(--mainFontSize) * 0.95);
    color: var(--SmartThemeBodyColor);
}

.behavior-textarea {
    width: 100%;
    min-height: 60px;
    background: var(--SmartThemeBlurTintColor);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 6px;
    padding: 12px;
    resize: vertical;
    font-family: inherit;
    font-size: calc(var(--mainFontSize) * 0.9);
    line-height: 1.4;
    transition: all 0.2s ease;
}

.behavior-textarea:focus {
    outline: none;
    border-color: var(--SmartThemeAccent);
    box-shadow: 0 0 0 2px var(--SmartThemeAccent30a);
}

/* ===== 确认对话框 ===== */
.xiaobaix-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--black50a);
    backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 3));
    -webkit-backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 3));
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease;
}

.xiaobaix-confirm-content {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 12px;
    width: 90%;
    max-width: 450px;
    padding: 20px;
    box-shadow: 0px 8px 32px var(--black70a);
    animation: slideIn 0.3s ease;
    text-align: center;
    color: var(--SmartThemeBodyColor);
}

.xiaobaix-confirm-message {
    margin-bottom: 24px;
    font-size: calc(var(--mainFontSize) * 1.05);
    line-height: 1.5;
}

.xiaobaix-confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.xiaobaix-confirm-yes,
.xiaobaix-confirm-no {
    padding: 10px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: calc(var(--mainFontSize) * 0.95);
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.xiaobaix-confirm-yes {
    background: var(--crimson);
    color: var(--white);
}

.xiaobaix-confirm-no {
    background: var(--SmartThemeBorder);
    color: var(--SmartThemeBodyColor);
}

.xiaobaix-confirm-yes:hover,
.xiaobaix-confirm-no:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--black30a);
}

/* ===== 统计编辑器 ===== */
.stats-editor {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.stats-section {
    background: var(--SmartThemeBlurTintColor);
    border: 1px solid var(--SmartThemeBorderColor);
    padding: 16px;
    border-radius: 8px;
}

.stats-section h3 {
    margin: 0 0 12px 0;
    color: var(--SmartThemeBodyColor);
    font-weight: 600;
    border-bottom: 1px solid var(--SmartThemeBorderColor);
    padding-bottom: 6px;
}

.stats-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.stats-field label {
    flex: 1;
    font-size: calc(var(--mainFontSize) * 0.9);
}

.stats-field input {
    width: 80px;
    background: var(--SmartThemeInputColor);
    color: var(--SmartThemeText);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 4px;
    padding: 6px;
    text-align: center;
}

/* ===== 消息预览插件样式 ===== */
#message_preview_btn {
    width: var(--bottomFormBlockSize);
    height: var(--bottomFormBlockSize);
    margin: 0;
    border: none;
    cursor: pointer;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 300ms;
    color: var(--SmartThemeBodyColor);
    font-size: var(--bottomFormIconSize);
}

#message_preview_btn:hover {
    opacity: 1;
    filter: brightness(1.2);
}

.message-preview-popup {
    max-height: 70vh;
    overflow-y: auto;
    padding: 10px;
}

.message-preview-content-box {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    white-space: pre-wrap;
    max-height: 70vh;
    overflow-y: auto;
    padding: 15px;
    background: #000000 !important;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    color: #ffffff !important;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 15px;
}

.mes_history_preview {
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
    color: #8B4513;
}

.mes_history_preview:hover {
    opacity: 1;
    color: #D2691E;
}

/* ===== 滚动条样式 ===== */
.memory-tab-content::-webkit-scrollbar,
.message-preview-content-box::-webkit-scrollbar {
    width: 6px;
}

.memory-tab-content::-webkit-scrollbar-track,
.message-preview-content-box::-webkit-scrollbar-track {
    background: var(--SmartThemeBlurTintColor);
    border-radius: 3px;
}

.memory-tab-content::-webkit-scrollbar-thumb,
.message-preview-content-box::-webkit-scrollbar-thumb {
    background: var(--SmartThemeBorderColor);
    border-radius: 3px;
}

.memory-tab-content::-webkit-scrollbar-thumb:hover,
.message-preview-content-box::-webkit-scrollbar-thumb:hover {
    background: var(--SmartThemeAccent);
}
